{"artifacts": [{"path": "webserver/webserver.exe"}, {"path": "webserver/webserver.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "set_property"], "files": ["webserver/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}, {"command": 1, "file": 0, "line": 9, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Ob0 /Od /RTC1 -std:c++20 -ZI"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "20"}, "sourceIndexes": [0]}], "id": "webserver::@bcd093f13c6838073cb0", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Ob0 /Od /RTC1 -ZI", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "webserver", "nameOnDisk": "webserver.exe", "paths": {"build": "webserver", "source": "webserver"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "webserver/webserver.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "webserver/webserver.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}