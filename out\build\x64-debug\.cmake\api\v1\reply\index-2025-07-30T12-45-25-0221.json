{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "cpack": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe", "ctest": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe", "root": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.29"}, "version": {"isDirty": false, "major": 3, "minor": 29, "patch": 5, "string": "3.29.5-msvc4", "suffix": "msvc4"}}, "objects": [{"jsonFile": "codemodel-v2-82037f583a40b75275e2.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-e7fc4888591985bda11e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-1d0b03d4ce34c6fcf002.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-d19162570acee6f721be.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-MicrosoftVS": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "cmakeFiles", "version": 1}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}], "responses": [{"jsonFile": "cache-v2-e7fc4888591985bda11e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-1d0b03d4ce34c6fcf002.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "codemodel-v2-82037f583a40b75275e2.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-d19162570acee6f721be.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}]}}}}