{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.8"}, "projectIndex": 0, "source": "."}, {"build": "webserver", "jsonFile": "directory-webserver-Debug-689ad6c2dc12cfff509b.json", "minimumCMakeVersion": {"string": "3.8"}, "parentIndex": 0, "projectIndex": 0, "source": "webserver", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "webserver", "targetIndexes": [0]}], "targets": [{"directoryIndex": 1, "id": "webserver::@bcd093f13c6838073cb0", "jsonFile": "target-webserver-Debug-8fe2981886f17ccf6754.json", "name": "webserver", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/cpp/webserver/out/build/x64-debug", "source": "C:/cpp/webserver"}, "version": {"major": 2, "minor": 7}}